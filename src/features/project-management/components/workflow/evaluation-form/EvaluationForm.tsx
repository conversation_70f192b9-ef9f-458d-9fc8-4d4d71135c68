'use client';

import type { EvaluationCriteria, OverallScore } from '../../../types/evaluation-form';
import { useProjectStepScoreCreate } from '@/features/project-management/hooks/useProjectStepScoreCreate';

import { useCoAgent, useCopilotChat } from '@copilotkit/react-core';
import { zodResolver } from '@hookform/resolvers/zod';
import { useParams } from 'next/navigation';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import {
  useClientProfile,
  useCurrentStep,
  useCurrentStepInfoIds,
  useEvaluationActions,
  useOverallScore,
  useScoreDetail,
} from '../../../stores/project-workflow-store';
import { SectionType } from '../../../types/evaluation-form';
import WorkflowNavigation from '../layout/WorkflowNavigation';
import EvaluationTable from './EvaluationTable';
import { GET_POSITION_BY_TYPE } from '@/features/project-management/constants/evaluation';
import { Role, TextMessage } from '@copilotkit/runtime-client-gql';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import type { ScoreDetail } from '@/features/project-management/types/evaluation';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import { compareObjectArray } from '@/shared/utils/compareObject';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { useChatBoxHide } from '@/features/project-management/stores/chatbox-store';
import type { infosQA } from '@/features/project-management/types/step';
import { AGENT_NAME_COPILOTKIT, AGENT_ROUTE_NAME, MESSAGE_SEND_ROUTE_AGENT } from '@/shared/constants/global';
import { ENameStateAgentCopilotkit } from '@/shared/enums/global';
import type { stateRouteAgent } from '@/shared/types/global';

// Zod schema for form validation - defined outside component to prevent recreation
const evaluationFormSchema = z.object({
  evaluationData: z.array(
    z.object({
      id: z.string(),
      criteria: z.string(),
      answer: z.string(),
      confidence: z.string().optional(),
      citation: z.string().optional(),
      criteriaType: z.string(),
      weight: z.string(),
      criteriaScore: z.string(),
      convertedScore: z.string(),
      type: z.string(),
      selectedOptionIndex: z.number().optional(),
    }),
  ),
});

type EvaluationFormData = z.infer<typeof evaluationFormSchema>;

// Memoized section component to prevent unnecessary re-renders
const EvaluationSection = React.memo(({
  title,
  sectionType,
  errors,
  disabled,
}: {
  title: string;
  sectionType: SectionType;
  errors: any;
  disabled: boolean;
}) => {
  return (
    <>
      <h3 className="text-lg font-medium mb-4">{title}</h3>
      <div className="space-y-6">
        <EvaluationTable sectionType={sectionType} disabled={disabled} />

        {errors?.evaluationData && (
          <p className="text-red-500 text-sm mt-2">
            {errors.evaluationData.message}
          </p>
        )}
      </div>
    </>
  );
});

EvaluationSection.displayName = 'EvaluationSection';

const EvaluationForm: React.FC = () => {
  // USING CUSTOM HOOK
  const currentStep = useCurrentStep();

  const currentStepInfoIds = useCurrentStepInfoIds();

  const clientProfile = useClientProfile();

  const overallScore = useOverallScore();

  const scoreDetailStore = useScoreDetail();

  const hide = useChatBoxHide();

  const { mutateAsync } = useUpdateStatusStep();

  const { createProject } = useProjectStepScoreCreate();

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const {
    updateStatus,
    completeStep,
    updateStepData,
    initializeEvaluation,
    getFinalOverallScore,
    updateInitialEvaluationData,
    updateScoreDetail,
    getNextStepId,
  } = useEvaluationActions();

  // USING useState

  const [isTaskRunning, setIsTaskRunning] = useState(true);

  const [isViewMode, setIsViewMode] = useState(false);

  const [isEditing, setIsEditing] = useState(false);

  // USING COPILOTKIT

  const { state: coAgentState, setState: setCoAgentsState, running } = useCoAgent<stateRouteAgent<any>>({
    name: AGENT_ROUTE_NAME,
    initialState: {},
  });

  const { appendMessage } = useCopilotChat();

  // GET ID PARAMS

  const params = useParams<{ id: string }>();

  const { data: scoreDetail } = useGetInfoDetail<ScoreDetail, infosQA>(currentStep?.id ?? '');

  // Get the client profile section as the evaluationCriteria for backward compatibility
  const evaluationCriteria = useMemo(() => clientProfile, [clientProfile]);

  // Initialize the store when the component mounts - only once
  useEffect(() => {
    initializeEvaluation();
  }, [initializeEvaluation]);

  // Initialize React Hook Form with Zod validation
  const {
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<EvaluationFormData>({
    resolver: zodResolver(evaluationFormSchema),
    defaultValues: {
      evaluationData: evaluationCriteria,
    },
  });

  // Update form values when evaluation criteria change - with debounce to prevent excessive updates
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setValue('evaluationData', evaluationCriteria);
    }, 100);
    return () => clearTimeout(timeoutId);
  }, [evaluationCriteria, setValue]);

  useEffect(() => {
    if (scoreDetail) {
      console.log(scoreDetail);
      updateScoreDetail(scoreDetail);
    }
  }, [scoreDetail, updateScoreDetail]);

  const handleSendMessage = useCallback(() => {
    if (scoreDetail?.stepInfoPrevious) {
      const previousStep = scoreDetail.stepInfoPrevious[0]?.infos ?? [];
      const documentUrl = previousStep[previousStep.length - 1];
      const questions = previousStep.slice(0, -1);

      const document_url = (documentUrl?.url ?? []).map((url: any) => ({
        id: url.id,
        url: url.url,
        originalname: url.name,
        filename: url.name,
        key: url.file,
      }));

      setCoAgentsState((prevState: any) => ({
        ...prevState,
        agent_name: AGENT_NAME_COPILOTKIT.SUMMARIZE,
        [ENameStateAgentCopilotkit.SUMMARIZE]: {
          ...prevState[ENameStateAgentCopilotkit.SUMMARIZE],
          initial_info: [...questions],
          document_url: [...document_url],
        },
      }));

      appendMessage(
        new TextMessage({
          content: MESSAGE_SEND_ROUTE_AGENT,
          role: Role.Developer,
        }),
      );
    }
  }, [scoreDetail, appendMessage]);

  // Re-Running copilotkit when data in DB
  useEffect(() => {
    if (!running && currentStep?.status !== EStatusTask.COMPLETED) {
      // FIXME: update later when using Copilotkit
      // handleSendMessage();
    }
  }, [running, currentStep, handleSendMessage]);

  const changeEditMode = (status: boolean) => {
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setIsEditing(status);
  };

  const changeStatusTaskRun = (status: boolean) => {
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setIsTaskRunning(status);
  };

  useEffect(() => {
    console.log('scoreDetailStore', scoreDetailStore);
    if (scoreDetailStore && Object.keys(scoreDetailStore).length) {
      updateInitialEvaluationData(scoreDetailStore as any);
      changeStatusTaskRun(false);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsViewMode(true);
      changeEditMode(false);
    } else {
      changeStatusTaskRun(true);
    }
  }, [scoreDetailStore, updateInitialEvaluationData]);

  useEffect(() => {
    const summarizeState = coAgentState[ENameStateAgentCopilotkit.SUMMARIZE];
    console.log(summarizeState);
    if (summarizeState && summarizeState.client_summarize_process && summarizeState.client_summarize_process === 'done' && isTaskRunning) {
      updateStatus(currentStep?.id ?? '', EStatusTask.IN_PROGRESS);
      updateInitialEvaluationData(summarizeState.qa_strategist_output);
      changeEditMode(true);
      changeStatusTaskRun(false);
    }
  }, [coAgentState, isTaskRunning, updateInitialEvaluationData, updateStatus, currentStep?.id]);

  const getPayloadCreateScore = (overallScore: OverallScore) => {
    const { data } = overallScore;
    return Object.values(data).map((score, index) => ({
      order: index,
      name: `name_${index}`,
      scores: score.map(item => ({
        criteria: item.criteria,
        type: item.type,
        answer: item.answer,
        confidence: item.confidence || '',
        citation: item.citation || '',
        criteriaType: item.criteriaType,
        weight: item.weight,
        criteriaScore: item.criteriaScore,
      })),
    }));
  };

  const setValueFiveTStateAgent = (overallScore: OverallScore) => {
    const { data } = overallScore;
    const items = Object.values(data).flat();
    return items.reduce((acc, i) => {
      const positionKey = GET_POSITION_BY_TYPE[i.type as keyof typeof GET_POSITION_BY_TYPE];
      acc[positionKey] = { ...i, id: positionKey.toString() };
      return acc;
    }, {} as Record<string, typeof items[number]>);
  };

  const sendStateToAgent = (five_t_input: Record<string, EvaluationCriteria>) => {
    setCoAgentsState(
      preAgent => (
        {
          ...preAgent,
          agent_name: AGENT_NAME_COPILOTKIT.ASSESSMENT,
          [ENameStateAgentCopilotkit.ASSESSMENT]: {
            five_t_input,
          },
        }
      ),
    );

    appendMessage(
      new TextMessage({
        content: MESSAGE_SEND_ROUTE_AGENT,
        role: Role.Developer,
      }),
    );
  };

  // FIXME: update later when using Copilotkit
  // const resetEvaluationData = () => {
  //   updateInitialEvaluationData(scoreDetailStore as any);
  // };

  // const toggleViewMode = () => {
  //   if (isEditing) {
  //     resetEvaluationData();
  //   }
  //   setIsEditing(prev => !prev);
  // };

  // Form submission handler - wrapped in useCallback to prevent recreation on each render
  const onSubmit = useCallback(async () => {
    if (!currentStep) {
      return;
    }

    // Check if all criteria have been evaluated
    const allEvaluated = evaluationCriteria.every((item: { answer: string }) => item.answer !== '');

    if (!allEvaluated) {
      toast.error('Please evaluate all criteria before submitting', {
        duration: 3000,
      });
      return;
    }

    // Calculate the final score first
    getFinalOverallScore();

    // Get the latest overall score after calculation
    const latestScore = overallScore;
    const five_t_input = setValueFiveTStateAgent(overallScore);

    // Update step data with the latest score

    // if (currentStep.status === EStatusTask.COMPLETED) {
    //   updateStepData(currentStep.id, latestScore);
    //   completeStep(currentStep.id);
    //   return;
    // }

    const stepInfos = getPayloadCreateScore(latestScore).map(item => ({
      order: item.order,
      infos: item.scores,
    }));

    const initialStepInfos = (scoreDetail?.stepInfo ?? []).map(item => ({
      order: item.order,
      infos: item.infos,
    }));

    const isChanged = compareObjectArray(stepInfos, initialStepInfos);
    await updateQuestionAnswer({
      stepInfos,
    }, currentStep.id);
    updateStepData(currentStep.id, latestScore);

    mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });

    if (currentStep.status !== EStatusTask.COMPLETED || (!isChanged && currentStep.status === EStatusTask.COMPLETED)) {
      sendStateToAgent(five_t_input);
      toast.success('Score calculation done', {
        duration: 3000,
      });

      // Reset data in DB
      const id = getNextStepId();
      updateQuestionAnswer({ stepInfos: [] }, id ?? '');
    }
    hide();
    setIsEditing(false);
    completeStep(currentStep.id);
  }, [currentStep, currentStepInfoIds, evaluationCriteria, getFinalOverallScore, overallScore, updateStepData, completeStep, createProject, params.id]);

  // Memoize the form submission handler
  const onCompleteHandler = useMemo(() => handleSubmit(onSubmit), [handleSubmit, onSubmit]);

  return (
    (isTaskRunning)
      ? (
          <div className="p-4 md:p-6 ">
            <div className="mb-1 md:mb-2">Analyzing</div>
            <ProjectCardSkeleton />

            {/* <WorkflowNavigation
              onComplete={onCompleteHandler}
              disableNext={isSubmitting || isTaskRunning}
              nextButtonText={isSubmitting ? 'Processing...' : 'Approve'}
              showPrevious={false}
            /> */}
          </div>
        )
      : (
          <>
            <form
              onSubmit={onCompleteHandler}
              className="space-y-6 border border-border rounded-lg p-4 md:p-6 m-4 md:m-6"
            >
              {/* Section 1 */}
              <EvaluationSection
                title="Section A - Client Profile"
                sectionType={SectionType.CLIENT_PROFILE}
                errors={errors}
                disabled={!isEditing}
              />

              <div className="mt-8 border-t border-border pt-6"></div>

              {/* Section 2 */}
              <EvaluationSection
                title="Section B - Financial Capacity & Collaboration History"
                sectionType={SectionType.FINANCIAL_CAPACITY}
                errors={errors}
                disabled={!isEditing}
              />

              <div className="mt-8 border-t border-border pt-6"></div>

              {/* Section 3 */}
              <EvaluationSection
                title="Section C - Collaboration & Working Process"
                sectionType={SectionType.COLLABORATION}
                errors={errors}
                disabled={!isEditing}
              />

              <div className="mt-8 border-t border-border pt-6"></div>

              {/* Section 4 */}
              <EvaluationSection
                title="Section D - Growth Potential & Strategic Value"
                sectionType={SectionType.GROWTH_POTENTIAL}
                errors={errors}
                disabled={!isEditing}
              />
            </form>

            <WorkflowNavigation
              onComplete={onCompleteHandler}
              disableNext={isSubmitting}
              nextButtonText={isSubmitting ? 'Processing...' : (!isEditing && isViewMode) ? 'Next Step' : 'Approve'}
              showPrevious={true}
            >
              { isViewMode && (
                <></>
                // <Button
                //   type="button"
                //   className={`bg-warning-400 text-white rounded-lg hover:bg-warning-500 ${isEditing ? 'bg-error-500 hover:bg-error-600' : ''}`}
                //   onClick={toggleViewMode}
                //   disabled={isSubmitting}
                // >
                //   {isEditing ? 'Cancel' : 'Edit'}
                // </Button>
              )}
            </WorkflowNavigation>

          </>
        )

  );
};

export default EvaluationForm;
