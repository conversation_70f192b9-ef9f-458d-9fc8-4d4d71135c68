'use client';

import { useEffect, useState } from 'react';
import { useCoAgent } from '@copilotkit/react-core';
import type { BriefAnalysisFlow } from '@/features/project-management/types/agent';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { useCurrentStep, useCurrentTask, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import type { StepInfosPayload } from '@/features/project-management/types/evaluation';
import type { EditorContentChanged, stateRouteAgent } from '@/shared/types/global';
import { AGENT_NAME_COPILOTKIT, AGENT_ROUTE_NAME } from '@/shared/constants/global';
import { ENameStateAgentCopilotkit } from '@/shared/enums/global';
import BaseBriefAnalysis from './BaseBriefAnalysis';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useGetInfoDetail } from '@/features/project-management/hooks';

type BriefAnalysisResponse = {
  infos: { value: string }[];
};

const BriefStandardizedWrapper: React.FC = () => {
  const [isShowEditButton, setIsShowEditButton] = useState(false);

  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  const [markdown, setMarkdown] = useState<string>('');

  const [form, setForm] = useState<string>('');

  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { mutateAsync } = useUpdateStatusStep();

  const currentStep = useCurrentStep();

  const currentTask = useCurrentTask();

  const currentStepId = currentStep?.id;

  const { data: briefStandardized } = useGetInfoDetail<BriefAnalysisResponse, any>(currentStep?.id ?? '');

  const {
    completeStep,
    updateStatus,
  } = useWorkflowActions();

  const { state } = useCoAgent<stateRouteAgent<BriefAnalysisFlow>>({
    name: AGENT_ROUTE_NAME,
    initialState: {
      agent_name: AGENT_NAME_COPILOTKIT.ANALYSIS,
    },
  });

  const updateMarkdownToState = (data: string) => {
    const updateState = () => {
      setMarkdown(data);
      setForm(data);
      setIsLoading(false);
    };
    updateState();
  };

  useEffect(() => {
    if (briefStandardized && briefStandardized?.stepInfo.length) {
      const markdown = briefStandardized.stepInfo[0]?.infos[0]?.value;

      updateMarkdownToState(markdown ?? '');

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsShowEditButton(true);
    }
  }, [briefStandardized]);

  useEffect(() => {
    const briefAnalysisState = state[ENameStateAgentCopilotkit.ANALYSIS];
    console.log('brief_analysis_output', briefAnalysisState);
    if (briefAnalysisState && briefAnalysisState.brief_analysis_output && briefAnalysisState.brief_analysis_process && briefAnalysisState.brief_analysis_process === 'done') {
      updateMarkdownToState(briefAnalysisState.brief_analysis_output);
    }
  }, [state]);

  const toggleEditMode = () => {
    setIsEditMode(prev => !prev);
  };

  const handleApprove = async () => {
    if (!currentStepId) {
      return;
    }

    setIsEditMode(false);

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: markdown }],
        },
      ],
    };

    await updateQuestionAnswer(payload, currentStepId);
    if (currentStep.status !== EStatusTask.COMPLETED) {
      mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
      updateStatus(currentStep?.id ?? '', EStatusTask.COMPLETED);
    }
    if (currentTask && currentTask.status !== EStatusTask.COMPLETED) {
      mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.COMPLETED });
      updateStatus(currentTask?.id ?? '', EStatusTask.COMPLETED, true);
    }

    completeStep(currentStepId);
  };

  const handleEditorChange = (data: EditorContentChanged) => {
    const { markdown } = data;
    setForm(markdown);
  };

  const discardChange = () => {
    setForm(markdown);
    setIsEditMode(false);
  };

  const confirmChange = () => {
    setMarkdown(form);
    setIsEditMode(false);
  };

  return (
    <BaseBriefAnalysis
      isLoading={isLoading}
      markdown={markdown}
      form={form}
      isEditMode={isEditMode}
      isShowEditButton={isShowEditButton}
      onEditToggle={toggleEditMode}
      onConfirmChange={confirmChange}
      onDiscardChange={discardChange}
      onEditorChange={handleEditorChange}
      onApprove={handleApprove}
    />
  );
};

export default BriefStandardizedWrapper;
