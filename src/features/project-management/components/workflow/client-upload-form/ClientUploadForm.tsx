'use client';

import Label from '@/shared/components/form/Label';
import Radio from '@/shared/components/form/input/Radio';
import FileUpload from '../initial-screening-form/FileUpload';
import React, { useEffect, useState } from 'react';
import type { IFileResponse, stateRouteAgent } from '@/shared/types/global';
import WorkflowNavigation from '../layout/WorkflowNavigation';
import { useClientFileUploaded, useCurrentStep, useCurrentTask, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import { useCoAgent, useCopilotChat } from '@copilotkit/react-core';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import type { BriefAnalysisFlow } from '@/features/project-management/types/agent';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';
import { Role, TextMessage } from 'node_modules/@copilotkit/runtime-client-gql/dist/client/types';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { ETypeFile, ProjectCampaignEnum } from '@/features/project-management/types/project';
import type { fileUploadResponse, TemplateFiles } from '@/features/project-management/types/project';
import { Env } from '@/core/config/Env';
import { AGENT_NAME_COPILOTKIT, AGENT_ROUTE_NAME, MESSAGE_SEND_ROUTE_AGENT } from '@/shared/constants/global';
import { ENameStateAgentCopilotkit } from '@/shared/enums/global';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { compareObjectArray } from '@/shared/utils/compareObject';
import { useGetListTemplates } from '@/features/project-management/hooks/useProjectTemplate';

// Service options data - moved outside component to avoid re-renders
const SERVICE_OPTIONS = [
  { id: 'corporate', label: 'Corporate', value: ProjectCampaignEnum.CORPORATE },
  { id: 'crisis-management', label: 'Crisis Management', value: ProjectCampaignEnum.CRISIS_MANAGEMENT },
  { id: 'event', label: 'Event', value: ProjectCampaignEnum.EVENT },
  { id: 'gr-advocacy', label: 'GR-Advocacy', value: ProjectCampaignEnum.GR_ADVOCACY },
  { id: 'imc', label: 'IMC', value: ProjectCampaignEnum.IMC },
  { id: 'market-research', label: 'Market Research', value: ProjectCampaignEnum.MARKET_RESEARCH },
  { id: 'media-relation-pr', label: 'Media Relation - PR', value: ProjectCampaignEnum.MEDIA_RELATION_PR },
  { id: 'mibrand-branding', label: 'Mibrand Branding', value: ProjectCampaignEnum.MI_BRAND_BRANDING },
  { id: 'product-launch', label: 'Product Launch', value: ProjectCampaignEnum.PRODUCT_LAUNCH },
  { id: 'social-digital-corporate', label: 'Social & Digital Corporate', value: ProjectCampaignEnum.SOCIAL_DIGITAL_CORPORATE },
  { id: 'social-media-digital-product', label: 'Social Media & Digital Product', value: ProjectCampaignEnum.SOCIAL_DIGITAL_PRODUCT },
  { id: 'tvc-video-production', label: 'TVC/Video Production', value: ProjectCampaignEnum.TVC_VIDEO_PRODUCTION },
];

const ClientUploadForm: React.FC = () => {
  const [_files, setFiles] = useState<IFileResponse[]>([]);

  const [isDisable, setIsDisable] = useState(false);

  const [initialFile, setInitialFile] = useState<IFileResponse[]>([]);

  const [templateFile, setTemplateFile] = useState<TemplateFiles[]>([]);

  const [selectedOption, setSelectedOption] = useState<ProjectCampaignEnum>(ProjectCampaignEnum.IMC); // Default value

  // Custom Hook

  const currentStep = useCurrentStep();

  const currentTask = useCurrentTask();

  const currentStepId = currentStep?.id;

  const clientFileUploaded = useClientFileUploaded();

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { setClientFileUploaded, getNextStepId } = useWorkflowActions();

  const { mutateAsync } = useUpdateStatusStep();

  const { data: fileUpload } = useGetInfoDetail<fileUploadResponse, any>(currentStep?.id ?? '');

  const { data: templates } = useGetListTemplates();

  useEffect(() => {
    if (templates) {
      const templateSelect = templates.filter(template => template.campaign === selectedOption);
      let urlOptions: TemplateFiles[] = [];
      templateSelect.forEach(template => urlOptions = [...urlOptions, ...template.files]);

      setTemplateFile(urlOptions);
    }
  }, [templates, selectedOption]);

  //  Set Agent

  const { setState: setCoAgentsState } = useCoAgent<stateRouteAgent<BriefAnalysisFlow>>({
    name: AGENT_ROUTE_NAME,
  });

  const { appendMessage } = useCopilotChat();

  const saveInitialFile = (file: IFileResponse[]) => {
    const setInitialFileStore = () => {
      setInitialFile(file);
      setFiles(file);
    };

    setInitialFileStore();
  };

  const updateInitialFile = () => {
    const setFile = () => {
      setIsDisable(true);
      const files = ((fileUpload?.stepInfo[0]?.infos[0]?.files ?? []).map(
        (file: any) => ({
          mimeType: file.type,
          originalname: file.name,
          key: file.file,
          filename: file.name,
          url: `${Env.NEXT_PUBLIC_API_SERVER}/public/${file.file}`,
          _id: file.id,
        }),
      ));

      const serviceOption = fileUpload?.stepInfo[0]?.infos[0]?.serviceOption ?? ProjectCampaignEnum.IMC;
      saveInitialFile(files);
      setClientFileUploaded(files);
      setSelectedOption(serviceOption);
    };

    setFile();
  };

  useEffect(() => {
    saveInitialFile(clientFileUploaded);
    if (fileUpload && fileUpload.stepInfo.length) {
      updateInitialFile();
    }
  }, [fileUpload]);

  // Separate effect to handle service option from API
  useEffect(() => {
    if (fileUpload && fileUpload.stepInfo.length) {
      // Check if there's a saved service option from API
      // Assuming the service option might be stored in a custom field or metadata
      const stepInfo = fileUpload.stepInfo[0];
      const savedServiceOption = (stepInfo as any)?.serviceOption || (stepInfo as any)?.metadata?.serviceOption;

      if (savedServiceOption && SERVICE_OPTIONS.some(option => option.id === savedServiceOption)) {
        setSelectedOption(savedServiceOption);
      }
    }
  }, [fileUpload]);

  const {
    completeStep,
  } = useWorkflowActions();

  const handleFilesChange = React.useCallback((uploadedFiles: IFileResponse[]) => {
    setFiles(uploadedFiles);
  }, []);

  const handleOptionSelect = (optionId: string) => {
    setSelectedOption(+optionId);
  };

  const handleSendMessage = () => {
    setCoAgentsState((prevState: any) => ({
      ...prevState,
      agent_name: AGENT_NAME_COPILOTKIT.ANSWER,
      [ENameStateAgentCopilotkit.ANALYSIS]: {
        ...prevState[ENameStateAgentCopilotkit.ANALYSIS],
        client_brief_url: getFile(_files),
        ...templateFile.reduce((result, template) => {
          if (template.type === ETypeFile.BRIEF_TEMPLATE) {
            result.template_brief_url = [
              ...(result.template_brief_url || []),
              ...getFile([template.file]),
            ];
          }
          if (template.type === ETypeFile.BRIEF_QUESTION) {
            result.question_brief_url = [
              ...(result.question_brief_url || []),
              ...getFile([template.file]),
            ];
          }
          return result;
        }, {} as any),
      },
    }));

    appendMessage(
      new TextMessage({
        content: MESSAGE_SEND_ROUTE_AGENT,
        role: Role.Developer,
      }),
    );
  };

  const onSubmit = async () => {
    if (!currentStepId) {
      return;
    }
    const payload = {
      stepInfos: [
        {
          order: 0,
          infos: [{
            files:
            _files.map(file => ({
              file: file.key,
              name: file.originalname,
              type: file.mimeType,
              id: file._id,
            })),
            serviceOption: selectedOption,
          }],
        },
      ],
    };

    const isChanged = !compareObjectArray(initialFile, _files);
    const nextStepId = getNextStepId();
    await updateQuestionAnswer(
      payload,
      currentStepId,
    );

    if (currentStep.status !== EStatusTask.COMPLETED || (isChanged && currentStep.status === EStatusTask.COMPLETED)) {
      handleSendMessage();
      updateQuestionAnswer({ stepInfos: [] }, nextStepId);
    }
    if (currentStep.status !== EStatusTask.COMPLETED) {
      mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
    }
    if (currentTask && (currentTask.status === EStatusTask.PENDING)) {
      mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.IN_PROGRESS });
    }

    completeStep(currentStepId);
  };

  return (
    <div className="relative">
      <div className="space-y-6 p-4 md:p-6">
        {/* Service Options Grid */}
        <div className="mb-6">
          <div className="grid grid-cols-3 gap-4">
            {SERVICE_OPTIONS.map(option => (
              <Radio
                disabled={isDisable}
                key={option.value}
                id={`service-${option.id}`}
                name="service-options"
                value={`${option.value}`}
                checked={+selectedOption === option.value}
                onChange={handleOptionSelect}
                label={option.label}
              />
            ))}
          </div>
        </div>

        <Label htmlFor="files" className="mb-1.5 block text-primary">
          Attached Files
        </Label>
        <FileUpload isDisable={isDisable} initialFile={initialFile} onFilesChange={handleFilesChange} />
      </div>

      <WorkflowNavigation
        onComplete={onSubmit}
        nextButtonText="Generate"
        showPrevious={false}
      />
    </div>
  );
};

export default ClientUploadForm;
